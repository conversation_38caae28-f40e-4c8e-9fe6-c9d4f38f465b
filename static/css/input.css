@tailwind base;
@tailwind components;
@tailwind utilities;

/* Material 3 Expressive Design System - Enhanced Sage-Green Palette */

/* Custom Material 3 Components */
@layer components {
  /* Enhanced Material 3 Buttons */
  .material-button-filled {
    @apply inline-flex items-center justify-center px-6 py-3 text-label-large font-medium rounded-full bg-primary-600 text-white hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2 transition-all duration-200 shadow-elevation-2 hover:shadow-elevation-3 min-h-[48px];
  }
  
  .material-button-outlined {
    @apply inline-flex items-center justify-center px-6 py-3 text-label-large font-medium rounded-full border-2 border-primary-600 text-primary-600 hover:bg-primary-50 dark:hover:bg-primary-900 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2 transition-all duration-200 min-h-[48px];
  }
  
  .material-button-text {
    @apply inline-flex items-center justify-center px-4 py-2 text-label-large font-medium rounded-full text-primary-600 hover:bg-primary-50 dark:hover:bg-primary-900 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2 transition-all duration-200 min-h-[48px];
  }
  
  .material-fab {
    @apply inline-flex items-center justify-center w-14 h-14 rounded-full bg-primary-600 text-white hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2 transition-all duration-200 shadow-elevation-3 hover:shadow-elevation-4;
  }

  /* Enhanced Material 3 Cards */
  .material-card {
    @apply bg-surface-50 dark:bg-surface-800 rounded-xl shadow-elevation-1 hover:shadow-elevation-2 transition-all duration-200 border border-surface-200 dark:border-surface-700;
  }
  
  .material-card-elevated {
    @apply bg-surface-50 dark:bg-surface-800 rounded-xl shadow-elevation-2 hover:shadow-elevation-3 transition-all duration-200 border border-surface-200 dark:border-surface-700;
  }

  .material-card-header {
    @apply px-6 py-6 border-b border-surface-200 dark:border-surface-700;
  }

  .material-card-content {
    @apply px-6 py-6;
  }

  /* Enhanced Material 3 Form Elements */
  .material-input {
    @apply w-full px-4 py-3 text-body-large bg-surface-50 dark:bg-surface-800 border border-surface-300 dark:border-surface-600 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 transition-all duration-200 placeholder-surface-500 dark:placeholder-surface-400 text-surface-900 dark:text-surface-100 min-h-[48px];
  }
  
  .material-textarea {
    @apply w-full px-4 py-3 text-body-large bg-surface-50 dark:bg-surface-800 border border-surface-300 dark:border-surface-600 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 transition-all duration-200 placeholder-surface-500 dark:placeholder-surface-400 text-surface-900 dark:text-surface-100 resize-y;
  }
  
  .material-select {
    @apply w-full px-4 py-3 text-body-large bg-surface-50 dark:bg-surface-800 border border-surface-300 dark:border-surface-600 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 transition-all duration-200 text-surface-900 dark:text-surface-100 min-h-[48px];
  }

  .material-label {
    @apply block text-label-large font-medium text-surface-900 dark:text-surface-100 mb-2;
  }

  .material-helper-text {
    @apply text-body-small text-surface-600 dark:text-surface-400 mt-1;
  }

  .material-subtitle {
    @apply text-body-medium text-surface-600 dark:text-surface-400 mt-1;
  }

  /* Enhanced Navigation Elements */
  .material-nav-item {
    @apply flex items-center px-4 py-3 text-label-large text-surface-700 dark:text-surface-200 hover:bg-surface-100 dark:hover:bg-surface-700 transition-colors rounded-lg min-h-[48px];
  }
  
  .material-nav-item-active {
    @apply flex items-center px-4 py-3 text-label-large text-primary-700 dark:text-primary-300 bg-primary-50 dark:bg-primary-900 transition-colors rounded-lg min-h-[48px];
  }

  /* Enhanced Status Indicators */
  .material-chip {
    @apply inline-flex items-center px-3 py-1 text-label-medium rounded-full bg-surface-100 dark:bg-surface-700 text-surface-700 dark:text-surface-300 border border-surface-200 dark:border-surface-600;
  }
  
  .material-chip-primary {
    @apply inline-flex items-center px-3 py-1 text-label-medium rounded-full bg-primary-100 dark:bg-primary-900 text-primary-800 dark:text-primary-200 border border-primary-200 dark:border-primary-700;
  }
  
  .material-chip-success {
    @apply inline-flex items-center px-3 py-1 text-label-medium rounded-full bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200 border border-green-200 dark:border-green-700;
  }
  
  .material-chip-warning {
    @apply inline-flex items-center px-3 py-1 text-label-medium rounded-full bg-amber-100 dark:bg-amber-900 text-amber-800 dark:text-amber-200 border border-amber-200 dark:border-amber-700;
  }
  
  .material-chip-error {
    @apply inline-flex items-center px-3 py-1 text-label-medium rounded-full bg-error-100 dark:bg-error-900 text-error-800 dark:text-error-200 border border-error-200 dark:border-error-700;
  }

  /* Enhanced Interactive Elements */
  .material-checkbox {
    @apply w-5 h-5 text-primary-600 bg-surface-50 dark:bg-surface-800 border-2 border-surface-300 dark:border-surface-600 rounded focus:ring-primary-500 focus:ring-2 transition-all duration-200;
  }
  
  .material-radio {
    @apply w-5 h-5 text-primary-600 bg-surface-50 dark:bg-surface-800 border-2 border-surface-300 dark:border-surface-600 focus:ring-primary-500 focus:ring-2 transition-all duration-200;
  }

  /* Enhanced Typography with Better Contrast */
  .material-headline {
    @apply text-headline-large font-normal text-surface-900 dark:text-surface-50 tracking-tight;
  }
  
  .material-title {
    @apply text-title-large font-medium text-surface-900 dark:text-surface-100;
  }
  
  .material-body {
    @apply text-body-large text-surface-700 dark:text-surface-200 leading-relaxed;
  }
  
  .material-caption {
    @apply text-body-small text-surface-600 dark:text-surface-400;
  }

  /* Enhanced State Layers */
  .material-state-layer {
    @apply relative overflow-hidden;
  }
  
  .material-state-layer::before {
    content: '';
    @apply absolute inset-0 bg-current opacity-0 transition-opacity duration-200 pointer-events-none;
  }
  
  .material-state-layer:hover::before {
    @apply opacity-8;
  }
  
  .material-state-layer:focus::before {
    @apply opacity-12;
  }
  
  .material-state-layer:active::before {
    @apply opacity-16;
  }

  /* Enhanced Notification Styles */
  .material-notification {
    @apply flex items-start p-4 rounded-lg border-l-4 bg-surface-50 dark:bg-surface-800 shadow-elevation-1;
  }
  
  .material-notification-info {
    @apply border-primary-500 bg-primary-50 dark:bg-primary-950;
  }
  
  .material-notification-success {
    @apply border-green-500 bg-green-50 dark:bg-green-950;
  }
  
  .material-notification-warning {
    @apply border-amber-500 bg-amber-50 dark:bg-amber-950;
  }
  
  .material-notification-error {
    @apply border-error-500 bg-error-50 dark:bg-error-950;
  }
}

/* Enhanced Dark Mode Utilities */
@layer utilities {
  .dark-mode-transition {
    @apply transition-colors duration-200;
  }
  
  .enhanced-contrast {
    @apply text-surface-900 dark:text-surface-50;
  }
  
  .enhanced-contrast-secondary {
    @apply text-surface-700 dark:text-surface-200;
  }
  
  .enhanced-contrast-tertiary {
    @apply text-surface-600 dark:text-surface-300;
  }
}
