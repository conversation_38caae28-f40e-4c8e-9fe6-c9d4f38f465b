{% extends "base.html" %}
{% block title %}Create Household - Garden Planner{% endblock %}
{% block content %}
<div class="max-w-2xl mx-auto">
    <div class="material-card-elevated p-8">
        <div class="text-center mb-8">
            <span class="material-icons text-5xl text-primary-600 dark:text-primary-400 mb-4 block">home</span>
            <h1 class="text-headline-medium font-normal text-surface-900 dark:text-surface-100 mb-2">Create Your Household</h1>
            <p class="text-body-large text-surface-600 dark:text-surface-400">Set up your garden planning household to get started</p>
        </div>

        <form method="post" action="/wizard/household" class="space-y-6" onsubmit="return handleFormSubmit(event)">
            <input type="hidden" name="csrf_token" value="{{ csrf_token }}">

            <div>
                <label for="name" class="material-label">Household Name</label>
                <input type="text"
                       id="name"
                       name="name"
                       required
                       class="material-input"
                       placeholder="e.g., Smith Family Garden"
                       onkeypress="handleEnterKey(event)">
                <div class="material-helper-text">Choose a name that represents your gardening space</div>
            </div>

            <div class="flex justify-between items-center pt-4">
                <a href="/auth/logout" class="material-button-text">
                    <span class="material-icons text-sm mr-2">arrow_back</span>
                    Back to Login
                </a>
                <button type="submit" class="material-button-filled">
                    <span class="mr-2">Create Household</span>
                    <span class="material-icons text-sm">arrow_forward</span>
                </button>
            </div>
        </form>
    </div>
</div>

<script>
function handleEnterKey(event) {
    if (event.key === 'Enter') {
        event.preventDefault();
        event.target.form.submit();
    }
}

function handleFormSubmit(event) {
    const nameInput = document.getElementById('name');
    if (!nameInput.value.trim()) {
        event.preventDefault();
        nameInput.focus();
        return false;
    }
    return true;
}
</script>
{% endblock %}
