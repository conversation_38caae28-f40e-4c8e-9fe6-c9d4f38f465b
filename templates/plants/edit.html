{% extends "base.html" %}
{% block title %}Edit Plant{% endblock %}
{% block content %}
<div class="max-w-2xl mx-auto">
    <div class="material-card">
        <div class="material-card-header">
            <h1 class="material-title">Edit Plant</h1>
            <p class="material-subtitle">Update plant information and details</p>
        </div>

        <form method="post" action="/plants/{{ plant.id }}/update" class="space-y-6" onsubmit="return handleFormSubmit(event)">
            <input type="hidden" name="csrf_token" value="{{ csrf_token }}">

            <div>
                <label for="name" class="material-label">Plant Name</label>
                <input type="text"
                       id="name"
                       name="name"
                       value="{{ plant.name }}"
                       required
                       class="material-input"
                       placeholder="e.g., Tomato Cherry Red"
                       onkeypress="handleEnterKey(event)">
                <div class="material-helper-text">Enter the common name of the plant</div>
            </div>

            <div>
                <label for="latin_name" class="material-label">Latin Name (Optional)</label>
                <input type="text"
                       id="latin_name"
                       name="latin_name"
                       value="{{ plant.latin_name or '' }}"
                       class="material-input"
                       placeholder="e.g., Solanum lycopersicum"
                       onkeypress="handleEnterKey(event)">
                <div class="material-helper-text">Scientific name of the plant</div>
            </div>

            <div>
                <label for="variety" class="material-label">Variety (Optional)</label>
                <input type="text"
                       id="variety"
                       name="variety"
                       value="{{ plant.variety or '' }}"
                       class="material-input"
                       placeholder="e.g., Cherry, Beefsteak, Roma"
                       onkeypress="handleEnterKey(event)">
                <div class="material-helper-text">Specific variety or cultivar</div>
            </div>

            <div>
                <label for="description" class="material-label">Description (Optional)</label>
                <textarea id="description"
                          name="description"
                          rows="4"
                          class="material-input"
                          placeholder="Any additional notes about this plant...">{{ plant.description or '' }}</textarea>
                <div class="material-helper-text">Growing notes, characteristics, etc.</div>
            </div>

            <div class="flex justify-between items-center pt-6">
                <a href="/plants/list" class="material-button-text">
                    <span class="material-icons text-sm mr-2">arrow_back</span>
                    Back to Plants
                </a>
                <button type="submit" class="material-button-filled">
                    <span class="material-icons text-sm mr-2">save</span>
                    Update Plant
                </button>
            </div>
        </form>
    </div>
</div>

<script>
function handleFormSubmit(event) {
    const nameInput = document.getElementById('name');

    if (!nameInput.value.trim()) {
        event.preventDefault();
        nameInput.focus();
        return false;
    }

    return true;
}

function handleEnterKey(event) {
    if (event.key === 'Enter') {
        event.preventDefault();
        const form = event.target.closest('form');
        if (form) {
            form.requestSubmit();
        }
    }
}
</script>
{% endblock %}
