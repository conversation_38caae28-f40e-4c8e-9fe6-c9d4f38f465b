{% extends "base.html" %}
{% block title %}Edit Seed{% endblock %}
{% block content %}
<div class="max-w-2xl mx-auto">
    <div class="material-card">
        <div class="material-card-header">
            <h1 class="material-title">Edit Seed</h1>
            <p class="material-subtitle">Update seed information and details</p>
        </div>

        <form method="post" action="/seeds/{{ seed.id }}/update" class="space-y-6" onsubmit="return handleFormSubmit(event)">
            <input type="hidden" name="csrf_token" value="{{ csrf_token }}">

            <div>
                <label for="name" class="material-label">Seed Name</label>
                <input type="text"
                       id="name"
                       name="name"
                       value="{{ seed.name }}"
                       required
                       class="material-input"
                       placeholder="e.g., Tomato Cherry Red"
                       onkeypress="handleEnterKey(event)">
                <div class="material-helper-text">Enter the name or variety of the seed</div>
            </div>

            <div>
                <label for="herba_id" class="material-label">Plant Type</label>
                <input type="number"
                       id="herba_id"
                       name="herba_id"
                       value="{{ seed.herba_id }}"
                       required
                       class="material-input"
                       placeholder="Enter herba ID"
                       onkeypress="handleEnterKey(event)">
                <div class="material-helper-text">Herba database ID for this plant type</div>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                    <label for="acquisition_year" class="material-label">Acquisition Year</label>
                    <input type="number"
                           id="acquisition_year"
                           name="acquisition_year"
                           value="{{ seed.acquisition_year }}"
                           required
                           min="1900"
                           max="2030"
                           class="material-input"
                           placeholder="2024"
                           onkeypress="handleEnterKey(event)">
                    <div class="material-helper-text">Year when seeds were acquired</div>
                </div>

                <div>
                    <label for="expiration_year" class="material-label">Expiration Year</label>
                    <input type="number"
                           id="expiration_year"
                           name="expiration_year"
                           value="{{ seed.expiration_year }}"
                           required
                           min="1900"
                           max="2040"
                           class="material-input"
                           placeholder="2027"
                           onkeypress="handleEnterKey(event)">
                    <div class="material-helper-text">Year when seeds expire</div>
                </div>
            </div>

            <div>
                <label for="origin" class="material-label">Origin (Optional)</label>
                <input type="text"
                       id="origin"
                       name="origin"
                       value="{{ seed.origin or '' }}"
                       class="material-input"
                       placeholder="e.g., Local farmer, Online store, Gift"
                       onkeypress="handleEnterKey(event)">
                <div class="material-helper-text">Where did you get these seeds?</div>
            </div>

            <div>
                <label for="note" class="material-label">Notes (Optional)</label>
                <textarea id="note"
                          name="note"
                          rows="4"
                          class="material-input"
                          placeholder="Any additional notes about these seeds...">{{ seed.note or '' }}</textarea>
                <div class="material-helper-text">Storage conditions, germination rate, etc.</div>
            </div>

            <div class="flex justify-between items-center pt-6">
                <a href="/seeds/list" class="material-button-text">
                    <span class="material-icons text-sm mr-2">arrow_back</span>
                    Back to Seeds
                </a>
                <button type="submit" class="material-button-filled">
                    <span class="material-icons text-sm mr-2">save</span>
                    Update Seed
                </button>
            </div>
        </form>
    </div>
</div>

<script>
function handleFormSubmit(event) {
    const nameInput = document.getElementById('name');
    const herba_idInput = document.getElementById('herba_id');
    const acquisitionYear = document.getElementById('acquisition_year');
    const expirationYear = document.getElementById('expiration_year');

    if (!nameInput.value.trim()) {
        event.preventDefault();
        nameInput.focus();
        return false;
    }

    if (!herba_idInput.value) {
        event.preventDefault();
        herba_idInput.focus();
        return false;
    }

    if (!acquisitionYear.value || !expirationYear.value) {
        event.preventDefault();
        if (!acquisitionYear.value) acquisitionYear.focus();
        else expirationYear.focus();
        return false;
    }

    return true;
}

function handleEnterKey(event) {
    if (event.key === 'Enter') {
        event.preventDefault();
        const form = event.target.closest('form');
        if (form) {
            form.requestSubmit();
        }
    }
}
</script>
{% endblock %}
