{% extends "base.html" %}
{% block title %}Add New Seed - Garden Planner{% endblock %}
{% block content %}
<div class="max-w-2xl mx-auto">
    <div class="material-card-elevated p-8">
        <div class="text-center mb-8">
            <span class="material-icons text-5xl text-primary-600 dark:text-primary-400 mb-4 block">inventory</span>
            <h1 class="text-headline-medium font-normal text-surface-900 dark:text-surface-100 mb-2">Add New Seed</h1>
            <p class="text-body-large text-surface-600 dark:text-surface-400">Add a new seed to your collection</p>
        </div>

        <form method="post" action="/seeds/create" class="space-y-6" onsubmit="return handleFormSubmit(event)">
            <input type="hidden" name="csrf_token" value="{{ csrf_token }}">

            <div>
                <label for="name" class="material-label">Seed Name</label>
                <input type="text"
                       id="name"
                       name="name"
                       required
                       class="material-input"
                       placeholder="e.g., Tomato Cherry Red"
                       onkeypress="handleEnterKey(event)">
                <div class="material-helper-text">Enter the name or variety of the seed</div>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                    <label for="acquisition_year" class="material-label">Acquisition Year</label>
                    <input type="number"
                           id="acquisition_year"
                           name="acquisition_year"
                           required
                           class="material-input"
                           min="1900"
                           max="2030"
                           value="{{ current_year }}"
                           onkeypress="handleEnterKey(event)">
                    <div class="material-helper-text">Year you acquired these seeds</div>
                </div>

                <div>
                    <label for="expiration_year" class="material-label">Expiration Year</label>
                    <input type="number"
                           id="expiration_year"
                           name="expiration_year"
                           required
                           class="material-input"
                           min="2024"
                           max="2040"
                           value="{{ current_year + 3 }}"
                           onkeypress="handleEnterKey(event)">
                    <div class="material-helper-text">Year when seeds expire</div>
                </div>
            </div>

            <div>
                <label for="herba_id" class="material-label">Related Plant</label>
                <select id="herba_id" name="herba_id" required class="material-select">
                    <option value="">Select a plant...</option>
                    {% for plant in plants %}
                    <option value="{{ plant.id }}">{{ plant.name }}{% if plant.latin_name %} ({{ plant.latin_name }}){% endif %}</option>
                    {% endfor %}
                </select>
                <div class="material-helper-text">Choose the plant this seed will grow into</div>
                <button type="button" id="add-new-plant-btn" class="material-button-text mt-2">
                    <span class="material-icons text-sm mr-2">add</span>
                    Add New Plant
                </button>
            </div>

            <!-- Modal Popup for New Plant -->
            <div id="new-plant-modal" class="fixed inset-0 z-50 overflow-y-auto hidden">
                <div class="flex items-center justify-center min-h-screen px-4">
                    <div class="fixed inset-0 bg-black bg-opacity-50 transition-opacity"></div>
                    <div class="relative material-card-elevated p-8 max-w-md w-full">
                        <div class="text-center mb-6">
                            <span class="material-icons text-4xl text-primary-600 dark:text-primary-400 mb-2 block">local_florist</span>
                            <h3 class="text-title-large font-medium text-surface-900 dark:text-surface-100">Add New Plant</h3>
                        </div>

                        <form id="new-plant-form" class="space-y-4">
                            <div>
                                <label for="plant_name" class="material-label">Plant Name</label>
                                <input type="text"
                                       id="plant_name"
                                       name="name"
                                       required
                                       class="material-input"
                                       placeholder="e.g., Cherry Tomato">
                            </div>

                            <div>
                                <label for="plant_latin_name" class="material-label">Latin Name (Optional)</label>
                                <input type="text"
                                       id="plant_latin_name"
                                       name="latin_name"
                                       class="material-input"
                                       placeholder="e.g., Solanum lycopersicum">
                            </div>

                            <div class="flex justify-end space-x-3 pt-4">
                                <button type="button" id="close-modal-btn" class="material-button-text">Cancel</button>
                                <button type="submit" class="material-button-filled">
                                    <span class="material-icons text-sm mr-2">add</span>
                                    Add Plant
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>

            <div>
                <label for="origin" class="material-label">Origin (Optional)</label>
                <input type="text"
                       id="origin"
                       name="origin"
                       class="material-input"
                       placeholder="e.g., Local garden center, Online store"
                       onkeypress="handleEnterKey(event)">
                <div class="material-helper-text">Where did you get these seeds?</div>
            </div>

            <div>
                <label for="note" class="material-label">Notes (Optional)</label>
                <textarea id="note"
                          name="note"
                          class="material-textarea"
                          rows="3"
                          placeholder="Any additional notes about these seeds..."></textarea>
                <div class="material-helper-text">Storage conditions, special instructions, etc.</div>
            </div>

            <div class="flex justify-between items-center pt-6">
                <a href="/seeds/list" class="material-button-text">
                    <span class="material-icons text-sm mr-2">arrow_back</span>
                    Back to Seeds
                </a>
                <button type="submit" class="material-button-filled">
                    <span class="material-icons text-sm mr-2">inventory</span>
                    Add Seed
                </button>
            </div>
        </form>
    </div>
</div>
<script>
document.addEventListener('DOMContentLoaded', function() {
    const modal = document.getElementById('new-plant-modal');
    const addPlantBtn = document.getElementById('add-new-plant-btn');
    const closeModalBtn = document.getElementById('close-modal-btn');
    const newPlantForm = document.getElementById('new-plant-form');
    const herba_select = document.getElementById('herba_id');

    // Show modal
    addPlantBtn.addEventListener('click', function() {
        modal.classList.remove('hidden');
        document.getElementById('plant_name').focus();
    });

    // Hide modal
    closeModalBtn.addEventListener('click', function() {
        modal.classList.add('hidden');
        newPlantForm.reset();
    });

    // Close modal on backdrop click
    modal.addEventListener('click', function(e) {
        if (e.target === modal) {
            modal.classList.add('hidden');
            newPlantForm.reset();
        }
    });

    // Handle new plant form submission
    newPlantForm.addEventListener('submit', function(event) {
        event.preventDefault();

        const formData = new FormData();
        formData.append('name', document.getElementById('plant_name').value);
        formData.append('latin_name', document.getElementById('plant_latin_name').value || '');
        formData.append('csrf_token', '{{ csrf_token }}');

        fetch('/plants/create', {
            method: 'POST',
            body: formData
        })
        .then(response => {
            if (response.ok) {
                return response.text();
            }
            throw new Error('Network response was not ok');
        })
        .then(data => {
            // Try to parse as JSON, but handle HTML responses too
            try {
                const jsonData = JSON.parse(data);
                if (jsonData.success && jsonData.plant) {
                    addPlantToSelect(jsonData.plant);
                    modal.classList.add('hidden');
                    newPlantForm.reset();
                    showSuccessMessage('Plant added successfully!');
                } else {
                    throw new Error(jsonData.message || 'Failed to create plant');
                }
            } catch (e) {
                // If response is HTML (redirect), assume success and reload
                if (data.includes('<!DOCTYPE html>')) {
                    showSuccessMessage('Plant added successfully! Please refresh to see it in the list.');
                    modal.classList.add('hidden');
                    newPlantForm.reset();
                } else {
                    throw e;
                }
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showErrorMessage('Failed to create plant: ' + error.message);
        });
    });

    function addPlantToSelect(plant) {
        const option = document.createElement('option');
        option.value = plant.id;
        option.textContent = plant.name + (plant.latin_name ? ` (${plant.latin_name})` : '');

        // Insert before the last option (which should be empty or "Select...")
        const lastOption = herba_select.options[herba_select.options.length - 1];
        herba_select.insertBefore(option, lastOption);

        // Select the new plant
        herba_select.value = plant.id;
    }

    function showSuccessMessage(message) {
        // Create a temporary success message
        const successDiv = document.createElement('div');
        successDiv.className = 'fixed top-4 right-4 bg-primary-600 text-white px-6 py-3 rounded-lg shadow-lg z-50';
        successDiv.textContent = message;
        document.body.appendChild(successDiv);

        setTimeout(() => {
            successDiv.remove();
        }, 3000);
    }

    function showErrorMessage(message) {
        // Create a temporary error message
        const errorDiv = document.createElement('div');
        errorDiv.className = 'fixed top-4 right-4 bg-error-600 text-white px-6 py-3 rounded-lg shadow-lg z-50';
        errorDiv.textContent = message;
        document.body.appendChild(errorDiv);

        setTimeout(() => {
            errorDiv.remove();
        }, 5000);
    }
});

function handleEnterKey(event) {
    if (event.key === 'Enter') {
        event.preventDefault();
        const form = event.target.closest('form');
        if (form) {
            const submitButton = form.querySelector('button[type="submit"]');
            if (submitButton && !submitButton.disabled) {
                submitButton.click();
            }
        }
    }
}

function handleFormSubmit(event) {
    const nameInput = document.getElementById('name');
    const herba_idSelect = document.getElementById('herba_id');
    const acquisitionYear = document.getElementById('acquisition_year');
    const expirationYear = document.getElementById('expiration_year');

    if (!nameInput.value.trim()) {
        event.preventDefault();
        nameInput.focus();
        return false;
    }

    if (!herba_idSelect.value) {
        event.preventDefault();
        herba_idSelect.focus();
        return false;
    }

    if (!acquisitionYear.value || !expirationYear.value) {
        event.preventDefault();
        if (!acquisitionYear.value) acquisitionYear.focus();
        else expirationYear.focus();
        return false;
    }

    return true;
}
</script>
{% endblock %}
