{% extends "base.html" %}
{% block title %}Plant Wishlist - Garden Planner{% endblock %}
{% block content %}
<div class="max-w-7xl mx-auto">
    <div class="flex flex-col sm:flex-row justify-between items-start sm:items-center mb-8 gap-4">
        <div>
            <h1 class="text-headline-large font-normal text-surface-900 dark:text-surface-100">Plant Wishlist</h1>
            <p class="text-body-large text-surface-600 dark:text-surface-400 mt-2">Plants you want to grow in your garden</p>
        </div>
        <div class="flex flex-col sm:flex-row gap-3">
            <a href="/wishlist/seeds" class="material-button-outlined">
                <span class="material-icons text-sm mr-2">inventory</span>
                Seed Wishlist
            </a>
            <a href="/plants/new" class="material-button-filled">
                <span class="material-icons text-sm mr-2">add</span>
                Add New Plant
            </a>
        </div>
    </div>

    <!-- Plant Grid -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
        {% for plant in plants %}
        <div class="material-card-elevated hover:shadow-elevation-4 transition-shadow">
            <div class="p-6">
                <div class="flex items-center mb-4">
                    <div class="w-12 h-12 bg-primary-100 dark:bg-primary-800 rounded-full flex items-center justify-center mr-4">
                        <span class="material-icons text-primary-600 dark:text-primary-400">local_florist</span>
                    </div>
                    <div class="flex-1">
                        <h3 class="text-title-medium font-medium text-surface-900 dark:text-surface-100">{{ plant.name }}</h3>
                        {% if plant.variety %}
                        <p class="text-body-medium text-surface-600 dark:text-surface-400">{{ plant.variety }}</p>
                        {% endif %}
                    </div>
                </div>

                <div class="space-y-3 mb-6">
                    {% if plant.latin_name %}
                    <div class="flex items-center text-body-small text-surface-600 dark:text-surface-400">
                        <span class="material-icons text-sm mr-2">science</span>
                        <span class="italic">{{ plant.latin_name }}</span>
                    </div>
                    {% endif %}

                    {% if plant.description %}
                    <p class="text-body-medium text-surface-700 dark:text-surface-300 line-clamp-3">
                        {{ plant.description | truncate(length=120) }}
                    </p>
                    {% endif %}

                    {% if plant.sowing_time %}
                    <div class="flex items-center text-body-small text-surface-600 dark:text-surface-400">
                        <span class="material-icons text-sm mr-2">schedule</span>
                        <span>Sowing: {{ plant.sowing_time }}</span>
                    </div>
                    {% endif %}
                </div>

                <div class="flex gap-3">
                    {% if plant.id in wishlist_ids %}
                    <form method="post" action="/wishlist/remove" class="flex-1">
                        <input type="hidden" name="csrf_token" value="{{ csrf_token }}">
                        <input type="hidden" name="item_type" value="plant">
                        <input type="hidden" name="item_id" value="{{ plant.id }}">
                        <button type="submit" class="material-button-outlined-error w-full">
                            <span class="material-icons text-sm mr-2">remove</span>
                            Remove
                        </button>
                    </form>
                    {% else %}
                    <form method="post" action="/wishlist/add" class="flex-1">
                        <input type="hidden" name="csrf_token" value="{{ csrf_token }}">
                        <input type="hidden" name="item_type" value="plant">
                        <input type="hidden" name="item_id" value="{{ plant.id }}">
                        <button type="submit" class="material-button-filled w-full">
                            <span class="material-icons text-sm mr-2">favorite</span>
                            Add to Wishlist
                        </button>
                    </form>
                    {% endif %}
                    <a href="/plants/{{ plant.id }}/edit" class="material-button-outlined">
                        <span class="material-icons text-sm">visibility</span>
                    </a>
                </div>
            </div>
        </div>
        {% endfor %}
    </div>

    {% if plants|length == 0 %}
    <div class="text-center py-16">
        <span class="material-icons text-6xl text-surface-400 dark:text-surface-500 mb-4 block">local_florist</span>
        <h3 class="text-title-large font-medium text-surface-900 dark:text-surface-100 mb-2">No plants available</h3>
        <p class="text-body-large text-surface-600 dark:text-surface-400 mb-8">Get started by adding some plants to the database.</p>
        <a href="/plants/new" class="material-button-filled">
            <span class="material-icons text-sm mr-2">add</span>
            Add Your First Plant
        </a>
    </div>
    {% endif %}
</div>
{% endblock %}
