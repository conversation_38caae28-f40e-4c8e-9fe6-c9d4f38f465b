pub mod auth;
pub mod plants;
pub mod plots;
pub mod seeds;
pub mod seasons;
pub mod season_plans;
pub mod admin;
pub mod notifications;
pub mod property;
pub mod households;
pub mod profile;
pub mod weather;
pub mod wishlist;

use actix_web::{web, HttpResponse, Result};
use actix_session::Session;
use crate::utils::templates::render_template_with_context;


pub mod wizard;

async fn test_route() -> Result<HttpResponse> {
    Ok(HttpResponse::Ok().body("Test route works!"))
}

async fn simple_wishlist_plants(session: Session, _pool: web::Data<crate::DbPool>) -> Result<HttpResponse> {
    if !crate::utils::auth::is_authenticated(&session) {
        return Ok(HttpResponse::Found()
            .append_header(("Location", "/auth/login"))
            .finish());
    }

    let mut ctx = tera::Context::new();
    ctx.insert("plants", &Vec::<String>::new());
    ctx.insert("wishlist_type", "plants");
    ctx.insert("wishlist_ids", &std::collections::HashSet::<i32>::new());
    ctx.insert("user_wishlist", &Vec::<String>::new());

    crate::utils::templates::render_template_with_context("wishlist/plants.html", &mut ctx, &session)
}

async fn simple_wishlist_seeds(session: Session, _pool: web::Data<crate::DbPool>) -> Result<HttpResponse> {
    if !crate::utils::auth::is_authenticated(&session) {
        return Ok(HttpResponse::Found()
            .append_header(("Location", "/auth/login"))
            .finish());
    }

    let mut ctx = tera::Context::new();
    ctx.insert("seeds", &Vec::<String>::new());
    ctx.insert("wishlist_type", "seeds");
    ctx.insert("wishlist_ids", &std::collections::HashSet::<i32>::new());
    ctx.insert("user_wishlist", &Vec::<String>::new());

    crate::utils::templates::render_template_with_context("wishlist/seeds.html", &mut ctx, &session)
}

async fn simple_notifications_api(session: Session) -> Result<HttpResponse> {
    if !crate::utils::auth::is_authenticated(&session) {
        return Ok(HttpResponse::Unauthorized().json(serde_json::json!({
            "error": "Not authenticated"
        })));
    }

    Ok(HttpResponse::Ok().json(serde_json::json!({
        "notifications": []
    })))
}

async fn simple_notifications_list(session: Session, _pool: web::Data<crate::DbPool>) -> Result<HttpResponse> {
    if !crate::utils::auth::is_authenticated(&session) {
        return Ok(HttpResponse::Found()
            .append_header(("Location", "/auth/login"))
            .finish());
    }

    let mut ctx = tera::Context::new();
    ctx.insert("notifications", &Vec::<String>::new());

    crate::utils::templates::render_template_with_context("notifications/list.html", &mut ctx, &session)
}

async fn test_template(session: Session) -> Result<HttpResponse> {
    let mut ctx = tera::Context::new();
    render_template_with_context("test.html", &mut ctx, &session)
}

async fn redirect_to_seeds_list(session: Session) -> Result<HttpResponse> {
    if !crate::utils::auth::is_authenticated(&session) {
        return Ok(HttpResponse::Found()
            .append_header(("Location", "/auth/login"))
            .finish());
    }
    Ok(HttpResponse::Found()
        .append_header(("Location", "/seeds/list"))
        .finish())
}

async fn redirect_to_plants_list(session: Session) -> Result<HttpResponse> {
    if !crate::utils::auth::is_authenticated(&session) {
        return Ok(HttpResponse::Found()
            .append_header(("Location", "/auth/login"))
            .finish());
    }
    Ok(HttpResponse::Found()
        .append_header(("Location", "/plants/list"))
        .finish())
}

pub fn init(cfg: &mut web::ServiceConfig) {
    // Register index route first
    cfg.route("/", web::get().to(index));
    cfg.route("/test", web::get().to(test_route));
    cfg.route("/test-template", web::get().to(test_template));

    // Wishlist and notification routes
    cfg.route("/wishlist/plants", web::get().to(simple_wishlist_plants));
    cfg.route("/wishlist/seeds", web::get().to(simple_wishlist_seeds));
    cfg.route("/notifications/list", web::get().to(simple_notifications_list));
    cfg.route("/api/notifications/recent", web::get().to(simple_notifications_api));

    // Add missing route redirects for common URLs
    cfg.route("/seeds", web::get().to(redirect_to_seeds_list));
    cfg.route("/plants", web::get().to(redirect_to_plants_list));

    plants::init(cfg);
    plots::init(cfg);
    seeds::init(cfg);
    seasons::init(cfg);
    season_plans::init(cfg);
    admin::init(cfg);
    auth::init(cfg);
    property::init(cfg);
    households::init(cfg);
    profile::init(cfg);
    weather::init(cfg);
    wishlist::init(cfg);
    notifications::init(cfg);
    wizard::init(cfg);

    // Add error handlers
    cfg.default_service(web::route().to(not_found_handler));
}


async fn index(session: Session, pool: web::Data<crate::DbPool>) -> Result<HttpResponse> {
    let mut ctx = tera::Context::new();

    // Add statistics if user is authenticated
    if crate::utils::auth::is_authenticated(&session) {
        if let Ok(mut conn) = pool.get() {
            use crate::schema::*;
            use diesel::prelude::*;

            // Get comprehensive statistics
            let total_properties = properties::table.count().get_result::<i64>(&mut conn).unwrap_or(0);
            let total_households = households::table.count().get_result::<i64>(&mut conn).unwrap_or(0);
            let total_plants = plants::table.count().get_result::<i64>(&mut conn).unwrap_or(0);
            let total_seeds = seeds::table.count().get_result::<i64>(&mut conn).unwrap_or(0);
            let total_seasons = seasons::table.count().get_result::<i64>(&mut conn).unwrap_or(0);
            let total_notifications = notifications::table.count().get_result::<i64>(&mut conn).unwrap_or(0);

            // Get user-specific statistics if user_id is available
            if let Ok(Some(user_id)) = session.get::<i32>("user_id") {
                let user_properties = properties::table
                    .filter(properties::owner_id.eq(user_id))
                    .count()
                    .get_result::<i64>(&mut conn)
                    .unwrap_or(0);

                let user_households = user_households::table
                    .filter(user_households::user_id.eq(user_id))
                    .count()
                    .get_result::<i64>(&mut conn)
                    .unwrap_or(0);

                // Get shared properties count
                let shared_properties = property_shares::table
                    .filter(property_shares::user_id.eq(user_id))
                    .count()
                    .get_result::<i64>(&mut conn)
                    .unwrap_or(0);

                // Get user notifications count
                let user_notifications = notifications::table
                    .filter(notifications::user_id.eq(user_id))
                    .count()
                    .get_result::<i64>(&mut conn)
                    .unwrap_or(0);

                // Get user wishlist count
                let user_wishlist = wishlists::table
                    .filter(wishlists::user_id.eq(user_id))
                    .count()
                    .get_result::<i64>(&mut conn)
                    .unwrap_or(0);

                ctx.insert("user_properties", &user_properties);
                ctx.insert("user_households", &user_households);
                ctx.insert("shared_properties", &shared_properties);
                ctx.insert("user_notifications", &user_notifications);
                ctx.insert("user_wishlist", &user_wishlist);
            }

            ctx.insert("total_properties", &total_properties);
            ctx.insert("total_households", &total_households);
            ctx.insert("total_plants", &total_plants);
            ctx.insert("total_seeds", &total_seeds);
            ctx.insert("total_seasons", &total_seasons);
            ctx.insert("total_notifications", &total_notifications);
        }
    }

    render_template_with_context("index.html", &mut ctx, &session)
}
async fn not_found_handler(session: Session) -> Result<HttpResponse> {
    let mut ctx = tera::Context::new();
    match render_template_with_context("errors/404.html", &mut ctx, &session) {
        Ok(response) => {
            let body = response.into_body();
            Ok(HttpResponse::NotFound().content_type("text/html").body(body))
        },
        Err(_) => Ok(HttpResponse::NotFound().body("404 Page not found"))
    }
}